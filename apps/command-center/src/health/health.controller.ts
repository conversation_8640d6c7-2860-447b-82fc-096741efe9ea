import { <PERSON>, Get, HttpStatus, Logger } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import type { PrismaService } from '../database/prisma/prisma.service'
import type { RedisService } from '../modules/redis/redis.service'

@ApiTags('Health')
@Controller('health')
export class HealthController {
  private readonly logger = new Logger(HealthController.name)

  constructor(
    private prisma: PrismaService,
    private redisService: RedisService
  ) {}

  @Get('live')
  @ApiOperation({
    summary: 'Liveness probe - checks if the application is running',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Application is alive' })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is not alive',
  })
  async checkLiveness() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
    }
  }

  @Get('ready')
  @ApiOperation({
    summary: 'Readiness probe - checks if the application is ready to serve traffic',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Application is ready' })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is not ready',
  })
  async checkReadiness() {
    try {
      // Simple database connection check
      await this.prisma.$queryRaw`SELECT 1`
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'healthy',
        },
      }
    } catch (_error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'unhealthy',
        },
      }
    }
  }

  @Get('startup')
  @ApiOperation({
    summary: 'Startup probe - checks if the application has started successfully',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application has started',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is still starting',
  })
  async checkStartup() {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'healthy',
        },
      }
    } catch (_error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'unhealthy',
        },
      }
    }
  }

  @Get()
  @ApiOperation({ summary: 'Full health check - comprehensive health status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All health checks passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'One or more health checks failed',
  })
  async check() {
    const memoryUsage = process.memoryUsage()
    const checks: Record<string, string> = {}
    let overallStatus = 'ok'

    // Database health check
    try {
      await this.prisma.$queryRaw`SELECT 1`
      checks.database = 'connected'
    } catch (error) {
      checks.database = 'disconnected'
      overallStatus = 'error'
      this.logger.error('Database health check failed:', error)
    }

    // Redis health check
    try {
      await this.redisService.set('health-check', 'test', 10)
      await this.redisService.get('health-check')
      checks.redis = 'connected'
    } catch (error) {
      checks.redis = 'disconnected'
      overallStatus = 'error'
      this.logger.error('Redis health check failed:', error)
    }

    // RabbitMQ status (currently disabled)
    checks.rabbitmq = 'disabled'

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        rss: memoryUsage.rss,
      },
      services: checks,
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Application metrics for monitoring' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Metrics retrieved successfully',
  })
  async getMetrics() {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      nodejs: {
        version: process.version,
        pid: process.pid,
        platform: process.platform,
        arch: process.arch,
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
      },
    }
  }

  @Get('health/db')
  @ApiOperation({ summary: 'Database health check only' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Database health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Database health check failed',
  })
  async checkDatabaseHealth() {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'healthy',
        },
      }
    } catch (_error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'unhealthy',
        },
      }
    }
  }

  @Get('health/redis')
  @ApiOperation({ summary: 'Redis health check only' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Redis health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Redis health check failed',
  })
  async checkRedisHealth() {
    try {
      await this.redisService.set('health-check', 'test', 10)
      const result = await this.redisService.get('health-check')
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          redis: 'healthy',
          test_result: result === 'test' ? 'passed' : 'failed',
        },
      }
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        checks: {
          redis: 'unhealthy',
          error: error.message,
        },
      }
    }
  }

  @Get('health/auth')
  @ApiOperation({ summary: 'Auth system health check' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Auth system health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Auth system health check failed',
  })
  async checkAuthHealth() {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'healthy',
          auth: 'basic_check_passed',
        },
      }
    } catch (_error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        checks: {
          database: 'unhealthy',
          auth: 'basic_check_failed',
        },
      }
    }
  }
}
